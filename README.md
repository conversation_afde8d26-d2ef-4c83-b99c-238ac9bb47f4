# 🔬 Curvature-Regularized Model Explorer

An interactive web-based tool for exploring regularized linear models with custom basis functions. This application allows you to visualize how curvature regularization affects model fitting, parameter evolution, and distortion curves.

## Features

- **Custom Basis Functions**: Define your own basis functions using mathematical expressions
- **Interactive Data Points**: Click to add data points directly on the plot
- **Real-time Regularization**: Adjust the curvature parameter `k` with live updates
- **Multiple Visualizations**:
  - Model fit with data points
  - Distortion curve D(k)
  - Parameter evolution θⱼ(k)
- **Mathematical Engine**: Robust numerical computation of curvature matrices and optimal parameters

## Mathematical Background

The tool implements curvature-regularized linear models of the form:

```
f(x) = Σⱼ θⱼ bⱼ(x)
```

Where:
- `bⱼ(x)` are user-defined basis functions
- `θⱼ` are parameters optimized via: `θ*(k) = (I + kR)⁻¹g`
- `R` is the curvature matrix: `Rᵢⱼ = ∫ bᵢ''(x) bⱼ''(x) dx`
- `g` is the projection vector: `g = X^T y`
- `k` is the curvature regularization parameter

## Installation

1. **Clone or download** this repository
2. **Install dependencies**:
   ```bash
   python install.py
   ```
   Or manually:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

1. **Start the application**:
   ```bash
   python main.py
   ```

2. **Define basis functions** in the text area (one per line):
   ```
   1
   x
   x^2
   sin(x)
   cos(x)
   ```

3. **Add data points** by clicking on the model plot

4. **Adjust regularization** using the slider or input field

5. **Explore visualizations** using the tabs:
   - **Model Fit**: See how your model fits the data
   - **Distortion Curve**: Understand the bias-variance tradeoff
   - **Parameter Evolution**: Watch how parameters change with regularization

## Example Basis Functions

- **Polynomial**: `1`, `x`, `x^2`, `x^3`
- **Trigonometric**: `sin(x)`, `cos(x)`, `sin(2*x)`, `cos(2*x)`
- **Exponential**: `exp(x)`, `exp(-x)`
- **Custom**: Any valid mathematical expression in terms of `x`

## Requirements

- Python 3.7+
- NumPy
- SciPy
- SymPy
- Matplotlib
- PyWebView

## Technical Details

- **Backend**: Python with mathematical engine using NumPy/SciPy
- **Frontend**: HTML/CSS/JavaScript with Plotly.js for visualization
- **Bridge**: PyWebView for seamless Python-JavaScript communication
- **Numerical Integration**: Trapezoidal rule for curvature matrix computation
- **Linear Algebra**: Robust solving with fallback to pseudo-inverse

## License

MIT License - Feel free to use and modify for educational and research purposes.

## Contributing

Contributions welcome! Areas for improvement:
- Additional basis function templates
- Advanced regularization methods
- Export functionality for plots and data
- Performance optimizations for large datasets
