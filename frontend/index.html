<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Curvature-Regularized Model Explorer</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🔬 Curvature-Regularized Model Explorer</h1>
            <p>Interactive tool for exploring regularized linear models with custom basis functions</p>
        </header>

        <div class="main-content">
            <!-- Control Panel -->
            <div class="control-panel">
                <div class="panel-section">
                    <h3>📊 Data Points</h3>
                    <div class="data-controls">
                        <button id="clearDataBtn" class="btn btn-danger">Clear All Points</button>
                        <div class="data-info">
                            <span id="dataPointCount">0 points</span>
                        </div>
                    </div>
                    <p class="help-text">Click on the plot to add data points</p>
                </div>

                <div class="panel-section">
                    <h3>🔧 Basis Functions</h3>
                    <div class="basis-input">
                        <textarea id="basisInput" placeholder="Enter basis functions (one per line):
1
x
x^2
sin(x)
cos(x)"></textarea>
                        <button id="updateBasisBtn" class="btn btn-primary">Update Basis</button>
                    </div>
                    <div id="basisStatus" class="status-message"></div>
                </div>

                <div class="panel-section">
                    <h3>⚙️ Regularization</h3>
                    <div class="regularization-controls">
                        <label for="kSlider">Curvature Parameter (k):</label>
                        <div class="slider-container">
                            <input type="range" id="kSlider" min="0" max="10" step="0.1" value="0">
                            <input type="number" id="kInput" min="0" max="100" step="0.1" value="0">
                        </div>
                        <div class="k-display">k = <span id="kValue">0.000</span></div>
                    </div>
                </div>

                <div class="panel-section">
                    <h3>📈 Current Parameters</h3>
                    <div id="parametersDisplay" class="parameters-display">
                        <p class="help-text">Parameters will appear here after setting basis functions and data points</p>
                    </div>
                </div>

                <div class="panel-section">
                    <h3>📋 Model Info</h3>
                    <div id="modelInfo" class="model-info">
                        <div class="info-item">
                            <span class="info-label">Data Points:</span>
                            <span id="infoDataPoints">0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Basis Functions:</span>
                            <span id="infoBasisCount">0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Current Distortion:</span>
                            <span id="infoDistortion">0.000</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Visualization Panel -->
            <div class="visualization-panel">
                <div class="plot-tabs">
                    <button class="tab-btn active" data-tab="model">Model Fit</button>
                    <button class="tab-btn" data-tab="distortion">Distortion Curve</button>
                    <button class="tab-btn" data-tab="parameters">Parameter Evolution</button>
                    <button class="tab-btn" data-tab="advanced">Advanced</button>
                </div>

                <div class="plot-container">
                    <div id="modelPlot" class="plot-content active"></div>
                    <div id="distortionPlot" class="plot-content"></div>
                    <div id="parametersPlot" class="plot-content"></div>
                    <div id="advancedPlot" class="plot-content">
                        <div class="advanced-content">
                            <div class="advanced-section">
                                <h4>🔬 Eigenbasis Decomposition</h4>
                                <div id="eigenInfo" class="eigen-info">
                                    <p class="help-text">Eigenvalues and shrinkage factors will appear here</p>
                                </div>
                                <div id="shrinkagePlot" class="mini-plot"></div>
                            </div>
                            <div class="advanced-section">
                                <h4>📊 Model Statistics</h4>
                                <div id="modelStats" class="model-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">Condition Number:</span>
                                        <span id="conditionNumber">-</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Effective DOF:</span>
                                        <span id="effectiveDOF">-</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Model Complexity:</span>
                                        <span id="modelComplexity">-</span>
                                    </div>
                                </div>
                            </div>
                            <div class="advanced-section">
                                <h4>💾 Export Data</h4>
                                <div class="export-controls">
                                    <button id="exportBtn" class="btn btn-primary">Export Model Data</button>
                                    <p class="help-text">Export current model configuration and results</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <footer>
            <div class="status-bar">
                <span id="statusMessage">Ready</span>
                <span id="lastUpdate"></span>
            </div>
        </footer>
    </div>

    <script src="app.js"></script>
</body>
</html>
