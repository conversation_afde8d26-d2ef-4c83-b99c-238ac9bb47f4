#!/usr/bin/env python3
"""
Curvature-Regularized Model Explorer
A web-based interactive tool for exploring regularized linear models with custom basis functions.
"""

import webview
import os
import sys
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.append(str(Path(__file__).parent))

from backend.api import API

def main():
    """Main entry point for the application."""
    
    # Get the directory containing this script
    app_dir = Path(__file__).parent
    html_file = app_dir / "frontend" / "index.html"
    
    # Create API instance
    api = API()
    
    # Create webview window
    window = webview.create_window(
        title="Curvature-Regularized Model Explorer",
        url=str(html_file),
        js_api=api,
        width=1400,
        height=900,
        min_size=(1000, 700),
        resizable=True
    )
    
    # Start the webview
    webview.start(debug=True)

if __name__ == "__main__":
    main()
