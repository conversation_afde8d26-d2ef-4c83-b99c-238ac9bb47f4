"""
PyWebView API backend for the Curvature-Regularized Model Explorer.
Provides the bridge between the HTML frontend and the mathematical engine.
"""

import numpy as np
from typing import Dict, List, Any
from .math_engine import MathEngine


class API:
    """API class for PyWebView backend."""
    
    def __init__(self):
        self.engine = MathEngine()
        self.current_k = 0.0
        
    def set_basis_functions(self, basis_list: List[str]) -> Dict[str, Any]:
        """
        Set basis functions from frontend.
        
        Args:
            basis_list: List of basis function strings
            
        Returns:
            Dict with success status and message
        """
        try:
            success = self.engine.parse_basis_functions(basis_list)
            if success:
                return {
                    'success': True,
                    'message': f'Successfully parsed {len(self.engine.basis_functions)} basis functions',
                    'basis_functions': self.engine.basis_functions_str
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to parse basis functions',
                    'basis_functions': []
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error: {str(e)}',
                'basis_functions': []
            }
    
    def add_data_point(self, x: float, y: float) -> Dict[str, Any]:
        """Add a data point."""
        try:
            self.engine.add_data_point(x, y)
            return {
                'success': True,
                'message': f'Added point ({x:.2f}, {y:.2f})',
                'n_points': len(self.engine.data_points['x'])
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error adding point: {str(e)}',
                'n_points': len(self.engine.data_points['x'])
            }
    
    def remove_data_point(self, index: int) -> Dict[str, Any]:
        """Remove a data point by index."""
        try:
            if 0 <= index < len(self.engine.data_points['x']):
                self.engine.remove_data_point(index)
                return {
                    'success': True,
                    'message': f'Removed point at index {index}',
                    'n_points': len(self.engine.data_points['x'])
                }
            else:
                return {
                    'success': False,
                    'message': 'Invalid point index',
                    'n_points': len(self.engine.data_points['x'])
                }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error removing point: {str(e)}',
                'n_points': len(self.engine.data_points['x'])
            }
    
    def clear_data_points(self) -> Dict[str, Any]:
        """Clear all data points."""
        try:
            self.engine.clear_data_points()
            return {
                'success': True,
                'message': 'Cleared all data points',
                'n_points': 0
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error clearing points: {str(e)}',
                'n_points': len(self.engine.data_points['x'])
            }
    
    def set_regularization(self, k: float) -> Dict[str, Any]:
        """Set the regularization parameter k."""
        try:
            self.current_k = max(0.0, float(k))
            return {
                'success': True,
                'message': f'Set k = {self.current_k:.3f}',
                'k': self.current_k
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error setting k: {str(e)}',
                'k': self.current_k
            }
    
    def get_model_curve(self, x_min: float = -5, x_max: float = 5, n_points: int = 200) -> Dict[str, Any]:
        """
        Get the model curve for current k value.
        
        Args:
            x_min: Minimum x value for curve
            x_max: Maximum x value for curve
            n_points: Number of points in curve
            
        Returns:
            Dict with x and y arrays for plotting
        """
        try:
            if not self.engine.basis_functions or not self.engine.data_points['x']:
                return {
                    'success': False,
                    'message': 'No basis functions or data points',
                    'x': [],
                    'y': []
                }
            
            x_curve = np.linspace(x_min, x_max, n_points)
            y_curve = self.engine.predict(x_curve, self.current_k)
            
            return {
                'success': True,
                'message': 'Model curve computed',
                'x': x_curve.tolist(),
                'y': y_curve.tolist()
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error computing curve: {str(e)}',
                'x': [],
                'y': []
            }
    
    def get_distortion_curve(self, k_min: float = 0, k_max: float = 10, n_points: int = 100) -> Dict[str, Any]:
        """
        Get the distortion curve D(k).
        
        Args:
            k_min: Minimum k value
            k_max: Maximum k value
            n_points: Number of points in curve
            
        Returns:
            Dict with k and distortion arrays for plotting
        """
        try:
            if not self.engine.basis_functions or not self.engine.data_points['x']:
                return {
                    'success': False,
                    'message': 'No basis functions or data points',
                    'k': [],
                    'distortion': []
                }
            
            k_values = np.linspace(k_min, k_max, n_points)
            distortion_values = self.engine.compute_distortion_curve(k_values)
            
            return {
                'success': True,
                'message': 'Distortion curve computed',
                'k': k_values.tolist(),
                'distortion': distortion_values.tolist(),
                'current_k': self.current_k,
                'current_distortion': self.engine.compute_distortion(self.current_k)
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error computing distortion curve: {str(e)}',
                'k': [],
                'distortion': []
            }
    
    def get_current_parameters(self) -> Dict[str, Any]:
        """Get current optimal parameters θ*(k)."""
        try:
            if not self.engine.basis_functions or not self.engine.data_points['x']:
                return {
                    'success': False,
                    'message': 'No basis functions or data points',
                    'parameters': [],
                    'basis_functions': []
                }
            
            theta = self.engine.solve_optimal_parameters(self.current_k)
            
            return {
                'success': True,
                'message': 'Parameters computed',
                'parameters': theta.tolist(),
                'basis_functions': self.engine.basis_functions_str,
                'k': self.current_k
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error computing parameters: {str(e)}',
                'parameters': [],
                'basis_functions': []
            }
    
    def get_parameter_evolution(self, k_min: float = 0, k_max: float = 10, n_points: int = 50) -> Dict[str, Any]:
        """Get parameter evolution θ_j(k) for visualization."""
        try:
            if not self.engine.basis_functions or not self.engine.data_points['x']:
                return {
                    'success': False,
                    'message': 'No basis functions or data points',
                    'k': [],
                    'parameters': [],
                    'basis_functions': []
                }
            
            k_values = np.linspace(k_min, k_max, n_points)
            evolution = self.engine.get_parameter_evolution(k_values)
            
            return {
                'success': True,
                'message': 'Parameter evolution computed',
                'k': evolution['k_values'].tolist(),
                'parameters': evolution['parameters'].tolist(),
                'basis_functions': self.engine.basis_functions_str
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error computing parameter evolution: {str(e)}',
                'k': [],
                'parameters': [],
                'basis_functions': []
            }
    
    def get_data_points(self) -> Dict[str, Any]:
        """Get current data points."""
        return {
            'success': True,
            'x': self.engine.data_points['x'],
            'y': self.engine.data_points['y'],
            'n_points': len(self.engine.data_points['x'])
        }
    
    def get_eigenbasis_decomposition(self) -> Dict[str, Any]:
        """Get eigenbasis decomposition of the curvature matrix."""
        try:
            return self.engine.get_eigenbasis_decomposition()
        except Exception as e:
            return {
                'success': False,
                'message': f'Error computing eigenbasis decomposition: {str(e)}'
            }

    def get_shrinkage_factors(self, k_min: float = 0, k_max: float = 10, n_points: int = 50) -> Dict[str, Any]:
        """Get shrinkage factors for eigenmode visualization."""
        try:
            k_values = np.linspace(k_min, k_max, n_points)
            return self.engine.get_shrinkage_factors(k_values)
        except Exception as e:
            return {
                'success': False,
                'message': f'Error computing shrinkage factors: {str(e)}'
            }

    def export_model_data(self) -> Dict[str, Any]:
        """Export current model data for saving/sharing."""
        try:
            data = {
                'basis_functions': self.engine.basis_functions_str,
                'data_points': self.engine.data_points,
                'current_k': self.current_k,
                'model_info': self.engine.get_model_info()
            }

            # Add current parameters if available
            if self.engine.data_points['x'] and self.engine.basis_functions:
                theta = self.engine.solve_optimal_parameters(self.current_k)
                data['current_parameters'] = theta.tolist()
                data['current_distortion'] = self.engine.compute_distortion(self.current_k)

            return {
                'success': True,
                'data': data,
                'timestamp': str(np.datetime64('now'))
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error exporting data: {str(e)}'
            }

    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive model information."""
        try:
            info = self.engine.get_model_info()
            info.update({
                'success': True,
                'current_k': self.current_k,
                'current_distortion': self.engine.compute_distortion(self.current_k) if self.engine.data_points['x'] else 0.0
            })
            return info
        except Exception as e:
            return {
                'success': False,
                'message': f'Error getting model info: {str(e)}'
            }
